"use client";

import { useState, useRef, useEffect } from "react";
import { useUser } from "@stackframe/stack";
import { Task, TaskSortOption } from "@/lib/db";
import { useReorderTasksMutation } from "@/lib/queries";
import { TaskItem } from "./task-item";
import { QuickAddTask } from "./quick-add-task";
import {
  DndContext,
  DragEndEvent,
  DragStartEvent,
  DragOverEvent,
  DragOverlay,
} from "@dnd-kit/core";
import {
  restrictToVerticalAxis,
} from "@dnd-kit/modifiers";
import {
  arrayMove,
  SortableContext,
  rectSortingStrategy,
} from "@dnd-kit/sortable";
import {
  useDragSensors,
  customCollisionDetection,
  dragMeasuring,
  createDragStartHandler,
  createDragOverHandler,
  createDragEndHandler,
} from "@/lib/drag-and-drop";

interface TaskListProps {
  tasks: Task[]; // Parent tasks only (filtered)
  allTasks?: Task[]; // All tasks including subtasks (for TaskItem to find its subtasks)
  onTaskUpdated: (updatedTask?: Task, statusChanged?: boolean) => void;
  onTaskDeleted: (deletedTaskId?: string) => void;
  onTasksReordered: (tasks: Task[]) => void;
  sortOption: TaskSortOption;
  listId: string;
  listColor?: string | null;
  taskCounts?: Record<string, number>;
  taskMode?: "completion" | "selection";
  selectedTaskIds?: Set<string>;
  onTaskSelectionChange?: (selectedIds: Set<string>) => void;
  lastSelectedTaskId?: string | null;
  showQuickAdd?: boolean;
  onAddTaskClick?: () => void;
  isInlineEditEnabled?: boolean;
  activeActionIconsTaskId?: string | null;
  onActionIconsChange?: (taskId: string | null) => void;
  onNavigateToTask?: (task: Task) => void; // Callback to switch to a different task
  isTagFiltered?: boolean; // Whether we're in tag-filtered view
  onDragStart?: () => void; // Callback when drag operation starts
}

export function TaskList({
  tasks,
  allTasks,
  onTaskUpdated,
  onTaskDeleted,
  onTasksReordered,
  sortOption,
  listId,
  listColor,
  taskCounts,
  taskMode = "completion",
  selectedTaskIds = new Set(),
  onTaskSelectionChange,
  lastSelectedTaskId = null,
  showQuickAdd = false,
  onAddTaskClick,
  isInlineEditEnabled = true,
  activeActionIconsTaskId = null,
  onActionIconsChange,
  onNavigateToTask,
  isTagFiltered = false,
  onDragStart,
}: TaskListProps) {
  const user = useUser();
  // Local state for drag and drop with immediate visual feedback
  const [isDragging, setIsDragging] = useState(false);
  const [activeTask, setActiveTask] = useState<Task | null>(null);
  const [localTasks, setLocalTasks] = useState<Task[]>(tasks);
  const lastDragOverRef = useRef<string | null>(null);
  const isReorderingRef = useRef(false);

  // TanStack Query mutation for reordering
  const reorderTasksMutation = useReorderTasksMutation(listId, sortOption);

  // Update local tasks when props change, but preserve local state during and after drag operations
  useEffect(() => {
    if (!isDragging && !isReorderingRef.current) {
      // Only update if the tasks have actually changed (not just reordered)
      const tasksChanged = tasks.length !== localTasks.length ||
        tasks.some((task, index) => task.id !== localTasks[index]?.id ||
          task.title !== localTasks[index]?.title ||
          task.status !== localTasks[index]?.status);

      if (tasksChanged) {
        setLocalTasks(tasks);
      }
    }
  }, [tasks, isDragging, localTasks]);

  // Use standardized drag and drop configuration
  const sensors = useDragSensors();





  // Use standardized drag handlers
  const handleDragStart = createDragStartHandler(
    setIsDragging,
    setActiveTask,
    localTasks,
    onDragStart,
    isReorderingRef,
    lastDragOverRef
  );

  const handleDragOver = createDragOverHandler(lastDragOverRef);

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;



    // Use standardized drag end cleanup
    const standardCleanup = createDragEndHandler(
      setIsDragging,
      setActiveTask,
      isReorderingRef,
      lastDragOverRef
    );
    standardCleanup();

    // Validate drag operation - only proceed if there's actually a position change
    if (!over || !active || active.id === over.id) {

      // No position change, don't trigger any updates
      return;
    }

    // Ensure both items exist in the current local tasks array
    const oldIndex = localTasks.findIndex((item) => item.id === active.id);
    const newIndex = localTasks.findIndex((item) => item.id === over.id);

    if (oldIndex === -1 || newIndex === -1) {
      console.warn('Drag operation failed: Invalid item indices', {
        activeId: active.id,
        overId: over.id,
        oldIndex,
        newIndex
      });
      return;
    }

    // Validate that we're not trying to move a task into itself or its subtask
    const draggedTask = localTasks[oldIndex];
    const targetTask = localTasks[newIndex];

    if (draggedTask.parent_task_id === targetTask.id ||
        targetTask.parent_task_id === draggedTask.id) {
      console.warn('Drag operation blocked: Cannot move task into its parent/child');
      return;
    }

    try {
      // Validate array move operation
      if (oldIndex < 0 || newIndex < 0 || oldIndex >= localTasks.length || newIndex >= localTasks.length) {
        console.error('Invalid indices for array move:', { oldIndex, newIndex, tasksLength: localTasks.length });
        return;
      }

      // Immediately update local state for visual feedback
      const newTasks = arrayMove(localTasks, oldIndex, newIndex);

      // Validate the result
      if (!newTasks || newTasks.length !== localTasks.length) {
        console.error('Array move produced invalid result');
        return;
      }

      // Update local state immediately for smooth visual feedback
      setLocalTasks(newTasks);

      // Call the parent callback for undo/redo tracking
      onTasksReordered(newTasks);

      // Update positions in the database using TanStack Query with optimistic updates
      if (user) {
        const taskIds = newTasks.map((task) => task?.id).filter(Boolean);

        // Validate task IDs
        if (taskIds.length !== newTasks.length) {
          console.error('Some tasks missing IDs after reorder');
          // Continue with valid IDs only
        }

        if (taskIds.length > 0) {
          reorderTasksMutation.mutate(
            { userId: user.id, taskIds },
            {
              onError: (error) => {
                console.error("Error reordering tasks:", error);

                // Revert local state to original tasks on error
                setLocalTasks(tasks);

                // Provide more specific error handling
                if (error.message?.includes('network') || error.message?.includes('fetch')) {
                  console.warn('Network error during reorder - changes reverted');
                } else if (error.message?.includes('timeout')) {
                  console.warn('Timeout during reorder - changes reverted');
                } else {
                  console.error('Unknown error during reorder - changes reverted:', error);
                }

                // Reset reordering flag to allow future operations
                isReorderingRef.current = false;
              },
              onSuccess: () => {
                // Keep local state until TanStack Query updates come through
                // This prevents visual jumps on successful reorder
                // The reordering flag will be reset in onSettled
              },
              onSettled: () => {
                // Always reset flag regardless of outcome
                setTimeout(() => {
                  isReorderingRef.current = false;
                }, 200);
              }
            }
          );
        } else {
          console.error('No valid task IDs to reorder');
          isReorderingRef.current = false;
        }
      } else {
        console.warn('No user available for reorder operation');
        isReorderingRef.current = false;
      }
    } catch (error) {
      console.error('Critical error in drag operation:', error);

      // Reset reordering flag on error
      try {
        isReorderingRef.current = false;
      } catch (flagError) {
        console.error('Failed to reset reordering flag:', flagError);
      }
    }
  };

  return (
    <div className={`space-y-2 ${isDragging ? 'dnd-context-dragging' : ''}`}>
      {sortOption === "position" ? (
        <DndContext
          id={`parent-tasks-${listId}`}
          sensors={sensors}
          collisionDetection={customCollisionDetection}
          onDragStart={handleDragStart}
          onDragOver={handleDragOver}
          onDragEnd={handleDragEnd}
          modifiers={[restrictToVerticalAxis]}
          measuring={dragMeasuring}
        >
          <SortableContext
            items={localTasks.map((task) => task.id)}
            strategy={rectSortingStrategy}
          >
            {localTasks.map((task) => (
              <TaskItem
                key={task.id}
                task={task}
                onUpdated={onTaskUpdated}
                onDeleted={onTaskDeleted}
                isDraggable={true}
                isAnyTaskDragging={isDragging}
                listId={listId}
                sortOption={sortOption}
                listColor={listColor}
                taskCounts={taskCounts}
                taskMode={taskMode}
                selectedTaskIds={selectedTaskIds}
                onTaskSelectionChange={onTaskSelectionChange}
                lastSelectedTaskId={lastSelectedTaskId}
                isInlineEditEnabled={isInlineEditEnabled}
                activeActionIconsTaskId={activeActionIconsTaskId}
                onActionIconsChange={onActionIconsChange}
                allTasks={allTasks || tasks}
                onNavigateToTask={onNavigateToTask}
                isTagFiltered={isTagFiltered}
              />
            ))}
          </SortableContext>

          {/* Drag Overlay to prevent card compression and maintain visual consistency */}
          <DragOverlay
            adjustScale={false}
            dropAnimation={null}
            style={{
              transformOrigin: '0 0',
              transition: 'none',
            }}
          >
            {activeTask ? (
              <div className="drag-overlay task-drag-overlay">
                <TaskItem
                  task={activeTask}
                  onUpdated={() => {}}
                  onDeleted={() => {}}
                  isDraggable={false}
                  isAnyTaskDragging={true}
                  listId={listId}
                  sortOption={sortOption}
                  listColor={listColor}
                  taskCounts={taskCounts}
                  taskMode={taskMode}
                  selectedTaskIds={selectedTaskIds}
                  onTaskSelectionChange={onTaskSelectionChange}
                  lastSelectedTaskId={lastSelectedTaskId}
                  forceSelected={true}
                  isInlineEditEnabled={isInlineEditEnabled}
                  activeActionIconsTaskId={activeActionIconsTaskId}
                  onActionIconsChange={onActionIconsChange}
                  allTasks={allTasks || tasks}
                  onNavigateToTask={onNavigateToTask}
                  isTagFiltered={isTagFiltered}
                />
              </div>
            ) : null}
          </DragOverlay>
        </DndContext>
      ) : (
        // Non-draggable list for when sorted by title or due date
        localTasks.map((task) => (
          <TaskItem
            key={task.id}
            task={task}
            onUpdated={onTaskUpdated}
            onDeleted={onTaskDeleted}
            isDraggable={false}
            isAnyTaskDragging={false}
            listId={listId}
            sortOption={sortOption}
            listColor={listColor}
            taskCounts={taskCounts}
            taskMode={taskMode}
            selectedTaskIds={selectedTaskIds}
            onTaskSelectionChange={onTaskSelectionChange}
            lastSelectedTaskId={lastSelectedTaskId}
            isInlineEditEnabled={isInlineEditEnabled}
            activeActionIconsTaskId={activeActionIconsTaskId}
            onActionIconsChange={onActionIconsChange}
            allTasks={allTasks || tasks}
            onNavigateToTask={onNavigateToTask}
            isTagFiltered={isTagFiltered}
          />
        ))
      )}

      {/* Quick Add Task Interface */}
      {showQuickAdd && onAddTaskClick && (
        <QuickAddTask
          onAddTaskClick={onAddTaskClick}
        />
      )}
    </div>
  );
}
